#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的MQTT功能
"""

import requests
import json
import time

def test_mqtt_api():
    """测试MQTT API"""
    base_url = "http://localhost:5000/api/v1"
    
    # 这里需要一个有效的token和device_id
    # 你需要先登录获取token，然后使用你的设备ID
    
    print("🔍 测试MQTT API修复")
    print("=" * 40)
    
    # 测试数据
    test_config = {
        "mqtt_broker_host": "localhost",
        "mqtt_broker_port": 1883,
        "mqtt_username": "",
        "mqtt_password": "",
        "mqtt_client_id": ""
    }
    
    print("测试配置:", json.dumps(test_config, indent=2))
    
    # 注意：这个测试需要有效的认证token
    # 实际使用时需要先登录获取token
    
    print("\n💡 要完整测试，请：")
    print("1. 启动服务器: python run.py")
    print("2. 在浏览器中登录并获取设备ID")
    print("3. 使用MQTT配置界面测试连接")
    
    return True

if __name__ == "__main__":
    test_mqtt_api()
