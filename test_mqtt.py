#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的MQTT连接测试脚本
用于验证MQTT服务器是否正常运行
"""

import paho.mqtt.client as mqtt
import time
import sys

def test_mqtt_connection(host='localhost', port=1883, username=None, password=None):
    """测试MQTT连接"""
    print(f"正在测试MQTT连接: {host}:{port}")
    
    # 连接结果
    connection_result = {'success': False, 'error': None}
    
    def on_connect(client, userdata, flags, rc):
        if rc == 0:
            print("✅ MQTT连接成功!")
            connection_result['success'] = True
            client.publish("test/topic", "Hello from test script!")
        else:
            error_messages = {
                1: "协议版本不正确",
                2: "客户端标识符无效",
                3: "服务器不可用", 
                4: "用户名或密码错误",
                5: "未授权"
            }
            error_msg = error_messages.get(rc, f"未知错误码: {rc}")
            print(f"❌ MQTT连接失败: {error_msg}")
            connection_result['error'] = error_msg
    
    def on_message(client, userdata, msg):
        print(f"📨 收到消息: {msg.topic} -> {msg.payload.decode()}")
    
    def on_disconnect(client, userdata, rc):
        if rc != 0:
            print(f"⚠️ 意外断开连接，错误码: {rc}")
        else:
            print("👋 正常断开连接")
    
    # 创建客户端
    client = mqtt.Client(
        client_id="test_client",
        protocol=mqtt.MQTTv311,
        clean_session=True
    )
    
    client.on_connect = on_connect
    client.on_message = on_message
    client.on_disconnect = on_disconnect
    
    # 设置认证
    if username:
        client.username_pw_set(username, password or '')
        print(f"使用用户名认证: {username}")
    else:
        print("使用匿名连接")
    
    try:
        # 连接
        client.connect(host, port, 60)
        client.loop_start()
        
        # 等待连接结果
        for i in range(50):  # 最多等待5秒
            if connection_result['success'] or connection_result['error']:
                break
            time.sleep(0.1)
        
        if connection_result['success']:
            # 订阅测试主题
            client.subscribe("test/topic")
            print("📡 已订阅 test/topic")
            
            # 等待一会儿接收消息
            time.sleep(2)
            
        # 清理
        client.disconnect()
        client.loop_stop()
        
        return connection_result['success']
        
    except Exception as e:
        print(f"❌ 连接异常: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔍 MQTT连接测试工具")
    print("=" * 40)
    
    # 测试不同的连接配置
    test_configs = [
        {'host': 'localhost', 'port': 1883},
        {'host': '127.0.0.1', 'port': 1883},
        {'host': 'test.mosquitto.org', 'port': 1883},
    ]
    
    success_count = 0
    
    for config in test_configs:
        print(f"\n测试配置: {config}")
        if test_mqtt_connection(**config):
            success_count += 1
        time.sleep(1)  # 间隔1秒
    
    print("\n" + "=" * 40)
    print(f"测试完成: {success_count}/{len(test_configs)} 个配置成功")
    
    if success_count == 0:
        print("\n💡 建议:")
        print("1. 检查MQTT服务器是否运行")
        print("2. 检查防火墙设置")
        print("3. 尝试安装并启动Mosquitto MQTT服务器")
        sys.exit(1)
    else:
        print("\n✅ 至少有一个MQTT服务器可用!")
        sys.exit(0)
