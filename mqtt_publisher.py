#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT消息发布工具
用于测试MQTT监控功能
"""

import paho.mqtt.client as mqtt
import time
import json
import sys

def publish_test_messages(host='localhost', port=1883, username=None, password=None):
    """发布测试消息"""
    print(f"连接到MQTT服务器: {host}:{port}")
    
    def on_connect(client, userdata, flags, rc):
        if rc == 0:
            print("✅ 连接成功!")
            
            # 发布一些测试消息
            test_messages = [
                {"topic": "test/topic", "payload": "Hello from publisher!"},
                {"topic": "sensor/temperature", "payload": json.dumps({"value": 23.5, "unit": "°C"})},
                {"topic": "device/status", "payload": json.dumps({"online": True, "timestamp": time.time()})},
                {"topic": "$SYS/broker/version", "payload": "test-broker-1.0"},
                {"topic": "data/sensor1/report", "payload": json.dumps({"humidity": 65, "temperature": 22.1})}
            ]
            
            for i, msg in enumerate(test_messages):
                client.publish(msg["topic"], msg["payload"])
                print(f"📤 发布消息 {i+1}: {msg['topic']} -> {msg['payload']}")
                time.sleep(1)
                
            print("✅ 所有测试消息已发布")
            
        else:
            print(f"❌ 连接失败，错误码: {rc}")
    
    def on_publish(client, userdata, mid):
        print(f"📨 消息 {mid} 发布确认")
    
    # 创建客户端
    client = mqtt.Client(
        client_id="test_publisher",
        protocol=mqtt.MQTTv311,
        clean_session=True
    )
    
    client.on_connect = on_connect
    client.on_publish = on_publish
    
    # 设置认证
    if username:
        client.username_pw_set(username, password or '')
        print(f"使用用户名认证: {username}")
    else:
        print("使用匿名连接")
    
    try:
        # 连接并发布
        client.connect(host, port, 60)
        client.loop_start()
        
        # 等待发布完成
        time.sleep(10)
        
        client.disconnect()
        client.loop_stop()
        
        return True
        
    except Exception as e:
        print(f"❌ 发布失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("📤 MQTT消息发布工具")
    print("=" * 40)
    
    # 可以修改这些参数
    config = {
        'host': 'localhost',
        'port': 1883,
        'username': None,  # 如果需要认证，设置用户名
        'password': None   # 如果需要认证，设置密码
    }
    
    if len(sys.argv) > 1:
        config['host'] = sys.argv[1]
    
    print(f"目标服务器: {config['host']}:{config['port']}")
    
    success = publish_test_messages(**config)
    
    if success:
        print("\n✅ 测试消息发布完成!")
        print("现在检查你的MQTT监控界面是否收到了这些消息")
    else:
        print("\n❌ 发布失败!")
        sys.exit(1)
